<UserControl x:Class="LabManagementSystem.Views.UserControls.PatientsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                  Text="إدارة المرضى" 
                  FontSize="24" FontWeight="Bold"
                  Margin="0,0,0,20"/>

        <!-- Search and Add -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBox Grid.Column="0"
                    materialDesign:HintAssist.Hint="البحث عن مريض..."
                    materialDesign:HintAssist.IsFloating="True"
                    Margin="0,0,10,0"/>

            <Button Grid.Column="1"
                   Content="إضافة مريض جديد"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   materialDesign:ButtonAssist.CornerRadius="20">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AccountPlus" 
                                                   Width="20" Height="20" 
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding}" 
                                      Margin="5,0,0,0" 
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>
        </Grid>

        <!-- Patients List -->
        <materialDesign:Card Grid.Row="2" 
                            materialDesign:ShadowAssist.ShadowDepth="Depth2">
            <DataGrid AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                     materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="80"/>
                    <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding FullName}" Width="200"/>
                    <DataGridTextColumn Header="تاريخ الميلاد" Binding="{Binding DateOfBirth, StringFormat=yyyy/MM/dd}" Width="120"/>
                    <DataGridTextColumn Header="الجنس" Binding="{Binding Gender}" Width="80"/>
                    <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                    <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="200"/>
                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                           ToolTip="عرض">
                                        <materialDesign:PackIcon Kind="Eye"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                           ToolTip="تعديل">
                                        <materialDesign:PackIcon Kind="Edit"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                           ToolTip="حذف">
                                        <materialDesign:PackIcon Kind="Delete"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
