<Window x:Class="LabManagementSystem.Views.PatientDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تفاصيل المريض" 
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="Account" 
                                   Width="32" Height="32" 
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="تفاصيل المريض" 
                      FontSize="24" FontWeight="Bold"
                      VerticalAlignment="Center" 
                      Margin="10,0,0,0"/>
        </StackPanel>

        <!-- Patient Details -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <materialDesign:Card materialDesign:ShadowAssist.ShadowDepth="Depth2">
                <Grid Margin="20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Patient ID -->
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم المريض:" FontWeight="Bold" Margin="0,5"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding Patient.Id}" Margin="10,5"/>

                    <!-- Full Name -->
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="الاسم الكامل:" FontWeight="Bold" Margin="0,5"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Patient.FullName}" Margin="10,5"/>

                    <!-- Date of Birth -->
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="تاريخ الميلاد:" FontWeight="Bold" Margin="0,5"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding Patient.DateOfBirth, StringFormat=yyyy/MM/dd}" Margin="10,5"/>

                    <!-- Gender -->
                    <TextBlock Grid.Row="3" Grid.Column="0" Text="الجنس:" FontWeight="Bold" Margin="0,5"/>
                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding GenderText}" Margin="10,5"/>

                    <!-- Phone -->
                    <TextBlock Grid.Row="4" Grid.Column="0" Text="الهاتف:" FontWeight="Bold" Margin="0,5"/>
                    <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding Patient.Phone}" Margin="10,5"/>

                    <!-- Email -->
                    <TextBlock Grid.Row="5" Grid.Column="0" Text="البريد الإلكتروني:" FontWeight="Bold" Margin="0,5"/>
                    <TextBlock Grid.Row="5" Grid.Column="1" Text="{Binding Patient.Email}" Margin="10,5"/>

                    <!-- National ID -->
                    <TextBlock Grid.Row="6" Grid.Column="0" Text="الرقم القومي:" FontWeight="Bold" Margin="0,5"/>
                    <TextBlock Grid.Row="6" Grid.Column="1" Text="{Binding Patient.NationalId}" Margin="10,5"/>

                    <!-- Address -->
                    <TextBlock Grid.Row="7" Grid.Column="0" Text="العنوان:" FontWeight="Bold" Margin="0,5" VerticalAlignment="Top"/>
                    <TextBlock Grid.Row="7" Grid.Column="1" Text="{Binding Patient.Address}" TextWrapping="Wrap" Margin="10,5"/>

                    <!-- Emergency Contact -->
                    <TextBlock Grid.Row="8" Grid.Column="0" Text="جهة الاتصال للطوارئ:" FontWeight="Bold" Margin="0,5"/>
                    <TextBlock Grid.Row="8" Grid.Column="1" Text="{Binding Patient.EmergencyContact}" Margin="10,5"/>

                    <!-- Emergency Phone -->
                    <TextBlock Grid.Row="9" Grid.Column="0" Text="هاتف الطوارئ:" FontWeight="Bold" Margin="0,5"/>
                    <TextBlock Grid.Row="9" Grid.Column="1" Text="{Binding Patient.EmergencyPhone}" Margin="10,5"/>
                </Grid>
            </materialDesign:Card>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                   HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Content="تعديل" 
                   Command="{Binding EditCommand}"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   materialDesign:ButtonAssist.CornerRadius="20"
                   Width="100"
                   Margin="0,0,10,0"/>

            <Button Content="إغلاق" 
                   Command="{Binding CloseCommand}"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   materialDesign:ButtonAssist.CornerRadius="20"
                   Width="100"
                   Margin="10,0,0,0"/>
        </StackPanel>
    </Grid>
</Window>
