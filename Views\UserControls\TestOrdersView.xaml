<UserControl x:Class="LabManagementSystem.Views.UserControls.TestOrdersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0" 
                  Text="طلبات الفحص" 
                  FontSize="24" FontWeight="Bold"
                  Margin="0,0,0,20"/>

        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBox Grid.Column="0"
                    materialDesign:HintAssist.Hint="البحث في طلبات الفحص..."
                    materialDesign:HintAssist.IsFloating="True"
                    Margin="0,0,10,0"/>

            <Button Grid.Column="1"
                   Content="طلب فحص جديد"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   materialDesign:ButtonAssist.CornerRadius="20">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="TestTubeEmpty" 
                                                   Width="20" Height="20" 
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding}" 
                                      Margin="5,0,0,0" 
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>
        </Grid>

        <materialDesign:Card Grid.Row="2" 
                            materialDesign:ShadowAssist.ShadowDepth="Depth2">
            <TextBlock Text="سيتم تطوير هذه الواجهة قريباً..." 
                      FontSize="16" 
                      HorizontalAlignment="Center" 
                      VerticalAlignment="Center"
                      Margin="20"/>
        </materialDesign:Card>
    </Grid>
</UserControl>
