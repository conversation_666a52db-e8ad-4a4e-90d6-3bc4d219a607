# نظام إدارة مختبر التحليلات المرضية
## Laboratory Management System

نظام شامل لإدارة مختبرات التحليلات الطبية مطور باستخدام WPF و .NET 8

## المتطلبات
- Windows 10 أو أحدث
- .NET 8.0 SDK
- Visual Studio 2022 أو Visual Studio Code

## التثبيت والتشغيل

### 1. تثبيت .NET 8.0 SDK
قم بتحميل وتثبيت .NET 8.0 SDK من الرابط التالي:
https://dotnet.microsoft.com/download/dotnet/8.0

### 2. تشغيل المشروع
```bash
# استنساخ المشروع أو تحميل الملفات
cd lab

# استعادة الحزم المطلوبة
dotnet restore

# بناء المشروع
dotnet build

# تشغيل التطبيق
dotnet run
```

## بيانات تسجيل الدخول الافتراضية
- **اس<PERSON> المستخدم:** admin
- **كلمة المرور:** admin123

## الميزات الرئيسية

### ✅ المكتملة
- نظام تسجيل الدخول والمصادقة
- واجهة رئيسية مع قائمة تنقل
- قاعدة بيانات SQLite
- نماذج البيانات الأساسية
- لوحة التحكم
- واجهة إدارة المرضى (أساسية)

### 🚧 قيد التطوير
- إدارة المرضى (إضافة، تعديل، حذف)
- إدارة طلبات الفحص
- إدخال وإدارة النتائج
- نظام التقارير
- إدارة أنواع التحليلات
- إدارة المستخدمين
- النسخ الاحتياطي

## هيكل المشروع
```
LabManagementSystem/
├── Models/              # نماذج البيانات
├── Views/               # واجهات المستخدم
│   ├── UserControls/    # عناصر التحكم المخصصة
│   ├── LoginWindow.xaml # نافذة تسجيل الدخول
│   └── MainWindow.xaml  # النافذة الرئيسية
├── ViewModels/          # نماذج العرض (MVVM)
├── Services/            # الخدمات
├── Data/                # قاعدة البيانات
├── Helpers/             # المساعدات والمحولات
└── App.xaml            # تطبيق WPF الرئيسي
```

## قاعدة البيانات
يستخدم النظام قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:
- Users (المستخدمون)
- Patients (المرضى)
- TestTypes (أنواع التحليلات)
- TestOrders (طلبات الفحص)
- TestResults (النتائج)

## التقنيات المستخدمة
- **WPF (Windows Presentation Foundation)** - واجهة المستخدم
- **Entity Framework Core** - التعامل مع قاعدة البيانات
- **SQLite** - قاعدة البيانات المحلية
- **MVVM Pattern** - نمط تصميم التطبيق
- **Material Design** - تصميم الواجهات
- **CommunityToolkit.Mvvm** - مكتبة MVVM

## الدعم والمساعدة
هذا مشروع تعليمي/تجريبي. للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع المطور.

## الترخيص
هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.
