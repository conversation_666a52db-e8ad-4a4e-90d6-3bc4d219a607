using System.Windows.Controls;
using LabManagementSystem.ViewModels;
using LabManagementSystem.Services;
using LabManagementSystem.Data;
using Microsoft.Extensions.DependencyInjection;
using System.Windows;

namespace LabManagementSystem.Views.UserControls
{
    /// <summary>
    /// Interaction logic for PatientsView.xaml
    /// </summary>
    public partial class PatientsView : UserControl
    {
        public PatientsView()
        {
            InitializeComponent();

            // إنشاء ViewModel مع الخدمات المطلوبة
            var serviceProvider = ((App)Application.Current).Host?.Services;
            if (serviceProvider != null)
            {
                var dbContext = serviceProvider.GetRequiredService<LabDbContext>();
                var patientService = new PatientService(dbContext);
                DataContext = new PatientsViewModel(patientService);
            }
        }
    }
}
