using System.Security.Cryptography;
using System.Text;
using LabManagementSystem.Data;
using LabManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace LabManagementSystem.Services
{
    public class AuthenticationService
    {
        private readonly LabDbContext _context;
        private User? _currentUser;

        public AuthenticationService(LabDbContext context)
        {
            _context = context;
        }

        public User? CurrentUser => _currentUser;

        public async Task<bool> LoginAsync(string username, string password)
        {
            try
            {
                var hashedPassword = HashPassword(password);
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == username && 
                                            u.PasswordHash == hashedPassword && 
                                            u.IsActive);

                if (user != null)
                {
                    _currentUser = user;
                    user.LastLogin = DateTime.Now;
                    await _context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public void Logout()
        {
            _currentUser = null;
        }

        public bool HasPermission(UserRole requiredRole)
        {
            if (_currentUser == null || !_currentUser.IsActive)
                return false;

            // Admin has all permissions
            if (_currentUser.Role == UserRole.Admin)
                return true;

            // Check specific role permissions
            return _currentUser.Role == requiredRole;
        }

        public bool CanAccessPatientData()
        {
            return HasPermission(UserRole.Doctor) || 
                   HasPermission(UserRole.Receptionist) || 
                   HasPermission(UserRole.Admin);
        }

        public bool CanModifyTestResults()
        {
            return HasPermission(UserRole.Doctor) || 
                   HasPermission(UserRole.Technician) || 
                   HasPermission(UserRole.Admin);
        }

        public bool CanManageUsers()
        {
            return HasPermission(UserRole.Admin);
        }

        public async Task<bool> ChangePasswordAsync(string currentPassword, string newPassword)
        {
            if (_currentUser == null)
                return false;

            var currentHashedPassword = HashPassword(currentPassword);
            if (_currentUser.PasswordHash != currentHashedPassword)
                return false;

            _currentUser.PasswordHash = HashPassword(newPassword);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<User?> CreateUserAsync(string username, string password, string fullName, 
                                                UserRole role, string? email = null, string? phone = null)
        {
            try
            {
                // Check if username already exists
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == username);

                if (existingUser != null)
                    return null;

                var user = new User
                {
                    Username = username,
                    PasswordHash = HashPassword(password),
                    FullName = fullName,
                    Role = role,
                    Email = email,
                    Phone = phone,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync();
                return user;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            try
            {
                _context.Users.Update(user);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> DeactivateUserAsync(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user != null)
                {
                    user.IsActive = false;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<List<User>> GetAllUsersAsync()
        {
            return await _context.Users.ToListAsync();
        }

        private string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }
    }
}
