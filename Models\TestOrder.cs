using System.ComponentModel.DataAnnotations;

namespace LabManagementSystem.Models
{
    public class TestOrder
    {
        public int Id { get; set; }
        
        [Required]
        public string OrderNumber { get; set; } = string.Empty;
        
        public int PatientId { get; set; }
        public virtual Patient Patient { get; set; } = null!;
        
        public int TestTypeId { get; set; }
        public virtual TestType TestType { get; set; } = null!;
        
        public int? DoctorId { get; set; }
        public virtual User? Doctor { get; set; }
        
        public DateTime OrderDate { get; set; } = DateTime.Now;
        
        public DateTime? SampleCollectionDate { get; set; }
        
        public DateTime? ExpectedCompletionDate { get; set; }
        
        public TestOrderStatus Status { get; set; } = TestOrderStatus.Pending;
        
        public decimal Price { get; set; }
        
        public bool IsPaid { get; set; } = false;
        
        public DateTime? PaidDate { get; set; }
        
        [MaxLength(500)]
        public string? Notes { get; set; }
        
        [MaxLength(100)]
        public string? SampleType { get; set; }
        
        public int? CreatedByUserId { get; set; }
        public virtual User? CreatedByUser { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedAt { get; set; }
        
        // Navigation properties
        public virtual ICollection<TestResult> TestResults { get; set; } = new List<TestResult>();
    }

    public enum TestOrderStatus
    {
        Pending = 1,        // في الانتظار
        SampleCollected = 2, // تم جمع العينة
        InProgress = 3,     // قيد التنفيذ
        Completed = 4,      // مكتمل
        Cancelled = 5,      // ملغي
        OnHold = 6         // معلق
    }
}
