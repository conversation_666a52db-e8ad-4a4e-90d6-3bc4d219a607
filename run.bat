@echo off
chcp 65001 >nul
echo ========================================
echo   نظام إدارة مختبر التحليلات المرضية
echo   Laboratory Management System
echo ========================================
echo.

echo التحقق من وجود .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: .NET SDK غير مثبت!
    echo.
    echo يرجى تحميل وتثبيت .NET 8.0 SDK من:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على .NET SDK
echo.

echo 📦 استعادة الحزم المطلوبة...
dotnet restore --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ خطأ في استعادة الحزم!
    pause
    exit /b 1
)

echo 🔨 بناء المشروع...
dotnet build --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ خطأ في بناء المشروع!
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.
echo 🚀 تشغيل التطبيق...
echo.
echo 🔑 بيانات تسجيل الدخول الافتراضية:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo 📝 ملاحظة: سيتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل
echo.
echo 🖥️ ستظهر نافذة تسجيل الدخول مع:
echo    - حقل اسم المستخدم
echo    - حقل كلمة المرور
echo    - زر دخول أزرق
echo    - زر خروج رمادي
echo.
dotnet run --project LabManagementSystem.csproj
