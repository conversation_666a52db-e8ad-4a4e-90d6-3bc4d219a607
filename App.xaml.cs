using System;
using System.Windows;
using LabManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace LabManagementSystem
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            try
            {
                using var context = new LabDbContext(new DbContextOptionsBuilder<LabDbContext>()
                    .UseSqlite("Data Source=lab_database.db")
                    .Options);

                context.Database.EnsureCreated();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء قاعدة البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }

            base.OnStartup(e);
        }
    }
}
