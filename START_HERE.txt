🚀 كيفية تشغيل نظام إدارة مختبر التحليلات المرضية
================================================================

⚠️ مطلوب أولاً: تثبيت .NET 8.0 SDK

📥 خطوات التثبيت:
1. اذهب إلى الرابط التالي:
   https://dotnet.microsoft.com/download/dotnet/8.0

2. اختر "Download .NET 8.0 SDK" للويندوز

3. قم بتشغيل الملف المحمل واتبع التعليمات

4. أعد تشغيل الكمبيوتر بعد التثبيت

🎯 بعد تثبيت .NET SDK:

الطريقة الأولى (السهلة):
- انقر نقراً مزدوجاً على ملف "run.bat"

الطريقة الثانية (يدوياً):
1. افتح Command Prompt في هذا المجلد
2. اكتب: dotnet run
3. اضغط Enter

🔑 بيانات تسجيل الدخول:
- اسم المستخدم: admin
- كلمة المرور: admin123

📋 الميزات المتاحة:
✅ تسجيل الدخول والخروج
✅ لوحة التحكم مع الإحصائيات
✅ إدارة المرضى (إضافة، تعديل، حذف، بحث)
✅ عرض تفاصيل المرضى
🚧 باقي الوظائف (قيد التطوير)

📞 في حالة وجود مشاكل:
1. تأكد من تثبيت .NET 8.0 SDK
2. أعد تشغيل الكمبيوتر
3. جرب تشغيل run.bat كمدير (Run as Administrator)

💾 ملفات مهمة:
- lab_database.db (قاعدة البيانات - ستُنشأ تلقائياً)
- README.md (دليل مفصل)
- INSTRUCTIONS.md (تعليمات إضافية)

🎉 استمتع باستخدام النظام!
