using System;
using System.Security.Cryptography;
using System.Text;
using Microsoft.EntityFrameworkCore;
using LabManagementSystem.Models;

namespace LabManagementSystem.Data
{
    public class LabDbContext : DbContext
    {
        public LabDbContext(DbContextOptions<LabDbContext> options) : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Patient> Patients { get; set; }
        public DbSet<TestType> TestTypes { get; set; }
        public DbSet<TestOrder> TestOrders { get; set; }
        public DbSet<TestResult> TestResults { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // User configurations
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasIndex(e => e.Username).IsUnique();
                entity.Property(e => e.Role).HasConversion<int>();
            });

            // Patient configurations
            modelBuilder.Entity<Patient>(entity =>
            {
                entity.Property(e => e.Gender).HasConversion<int>();
                entity.HasIndex(e => e.NationalId).IsUnique();
            });

            // TestType configurations
            modelBuilder.Entity<TestType>(entity =>
            {
                entity.Property(e => e.Price).HasPrecision(10, 2);
                entity.Property(e => e.Category).HasConversion<int>();
            });

            // TestOrder configurations
            modelBuilder.Entity<TestOrder>(entity =>
            {
                entity.HasIndex(e => e.OrderNumber).IsUnique();
                entity.Property(e => e.Price).HasPrecision(10, 2);
                entity.Property(e => e.Status).HasConversion<int>();
                
                entity.HasOne(e => e.Patient)
                    .WithMany(p => p.TestOrders)
                    .HasForeignKey(e => e.PatientId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.TestType)
                    .WithMany(t => t.TestOrders)
                    .HasForeignKey(e => e.TestTypeId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.Doctor)
                    .WithMany()
                    .HasForeignKey(e => e.DoctorId)
                    .OnDelete(DeleteBehavior.SetNull);
                
                entity.HasOne(e => e.CreatedByUser)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // TestResult configurations
            modelBuilder.Entity<TestResult>(entity =>
            {
                entity.Property(e => e.Status).HasConversion<int>();
                
                entity.HasOne(e => e.TestOrder)
                    .WithMany(o => o.TestResults)
                    .HasForeignKey(e => e.TestOrderId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.TestType)
                    .WithMany(t => t.TestResults)
                    .HasForeignKey(e => e.TestTypeId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.Technician)
                    .WithMany()
                    .HasForeignKey(e => e.TechnicianId)
                    .OnDelete(DeleteBehavior.SetNull);
                
                entity.HasOne(e => e.ReviewedByDoctor)
                    .WithMany()
                    .HasForeignKey(e => e.ReviewedByDoctorId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Seed initial data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // إنشاء مستخدم إداري افتراضي
            var adminPasswordHash = HashPassword("admin123");
            
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    Username = "admin",
                    PasswordHash = adminPasswordHash,
                    FullName = "مدير النظام",
                    Role = UserRole.Admin,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                }
            );

            // إضافة بعض أنواع التحليلات الأساسية
            modelBuilder.Entity<TestType>().HasData(
                new TestType { Id = 1, Name = "تحليل دم شامل", Description = "فحص شامل لمكونات الدم", Price = 50, Category = TestCategory.Hematology, Unit = "خلية/مل", NormalRange = "4000-11000", IsActive = true },
                new TestType { Id = 2, Name = "سكر الدم", Description = "قياس مستوى الجلوكوز في الدم", Price = 25, Category = TestCategory.Biochemistry, Unit = "مجم/دل", NormalRange = "70-100", IsActive = true },
                new TestType { Id = 3, Name = "وظائف الكلى", Description = "فحص وظائف الكلى", Price = 75, Category = TestCategory.Biochemistry, Unit = "مجم/دل", NormalRange = "0.6-1.2", IsActive = true },
                new TestType { Id = 4, Name = "وظائف الكبد", Description = "فحص وظائف الكبد", Price = 80, Category = TestCategory.Biochemistry, Unit = "وحدة/لتر", NormalRange = "7-56", IsActive = true },
                new TestType { Id = 5, Name = "الدهون الثلاثية", Description = "قياس مستوى الدهون الثلاثية", Price = 30, Category = TestCategory.Biochemistry, Unit = "مجم/دل", NormalRange = "<150", IsActive = true }
            );
        }

        private string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }
    }
}
