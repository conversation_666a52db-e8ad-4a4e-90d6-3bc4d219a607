using System;
using System.Windows;
using System.Windows.Controls;
using LabManagementSystem.ViewModels;
using LabManagementSystem.Services;
using LabManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace LabManagementSystem.Views.UserControls
{
    /// <summary>
    /// Interaction logic for PatientsView.xaml
    /// </summary>
    public partial class PatientsView : UserControl
    {
        public PatientsView()
        {
            InitializeComponent();

            // إنشاء ViewModel مع الخدمات المطلوبة
            try
            {
                var options = new DbContextOptionsBuilder<LabDbContext>()
                    .UseSqlite("Data Source=lab_database.db")
                    .Options;
                var dbContext = new LabDbContext(options);
                var patientService = new PatientService(dbContext);
                DataContext = new PatientsViewModel(patientService);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل واجهة المرضى: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
