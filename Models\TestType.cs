using System.ComponentModel.DataAnnotations;

namespace LabManagementSystem.Models
{
    public class TestType
    {
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        public decimal Price { get; set; }
        
        [MaxLength(50)]
        public string? Unit { get; set; }
        
        [MaxLength(100)]
        public string? NormalRange { get; set; }
        
        public TestCategory Category { get; set; }
        
        public int EstimatedDurationMinutes { get; set; } = 60;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        // Navigation properties
        public virtual ICollection<TestOrder> TestOrders { get; set; } = new List<TestOrder>();
        public virtual ICollection<TestResult> TestResults { get; set; } = new List<TestResult>();
    }

    public enum TestCategory
    {
        Hematology = 1,      // أمراض الدم
        Biochemistry = 2,    // الكيمياء الحيوية
        Microbiology = 3,    // الأحياء الدقيقة
        Immunology = 4,      // المناعة
        Pathology = 5,       // علم الأمراض
        Endocrinology = 6,   // الغدد الصماء
        Cardiology = 7,      // القلب
        Other = 8            // أخرى
    }
}
