using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LabManagementSystem.Models;
using LabManagementSystem.Services;
using System.Windows;
using System.Windows.Threading;

namespace LabManagementSystem.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly DispatcherTimer _timer;

        [ObservableProperty]
        private object? currentView;

        [ObservableProperty]
        private string currentUserName = "مستخدم غير معروف";

        [ObservableProperty]
        private bool isAdmin = false;

        [ObservableProperty]
        private string statusMessage = "جاهز";

        [ObservableProperty]
        private DateTime currentDateTime = DateTime.Now;

        public MainViewModel()
        {
            // إعداد المؤقت لتحديث الوقت
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _timer.Tick += (s, e) => CurrentDateTime = DateTime.Now;
            _timer.Start();

            // تحديد المستخدم الحالي
            UpdateCurrentUser();

            // عرض لوحة التحكم افتراضياً
            NavigateToView("Dashboard");
        }

        [RelayCommand]
        private void Navigate(string viewName)
        {
            NavigateToView(viewName);
        }

        [RelayCommand]
        private void Logout()
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", 
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                // إغلاق النافذة الحالية
                Application.Current.Windows.OfType<Views.MainWindow>().FirstOrDefault()?.Close();
                
                // فتح نافذة تسجيل الدخول
                var loginWindow = new Views.LoginWindow();
                loginWindow.Show();
            }
        }

        private void NavigateToView(string viewName)
        {
            try
            {
                switch (viewName)
                {
                    case "Dashboard":
                        CurrentView = CreateDashboardView();
                        StatusMessage = "لوحة التحكم";
                        break;
                    case "Patients":
                        CurrentView = CreatePatientsView();
                        StatusMessage = "إدارة المرضى";
                        break;
                    case "TestOrders":
                        CurrentView = CreateTestOrdersView();
                        StatusMessage = "طلبات الفحص";
                        break;
                    case "TestResults":
                        CurrentView = CreateTestResultsView();
                        StatusMessage = "النتائج";
                        break;
                    case "Reports":
                        CurrentView = CreateReportsView();
                        StatusMessage = "التقارير";
                        break;
                    case "TestTypes":
                        CurrentView = CreateTestTypesView();
                        StatusMessage = "أنواع التحليلات";
                        break;
                    case "Users":
                        if (IsAdmin)
                        {
                            CurrentView = CreateUsersView();
                            StatusMessage = "إدارة المستخدمين";
                        }
                        break;
                    case "Settings":
                        CurrentView = CreateSettingsView();
                        StatusMessage = "الإعدادات";
                        break;
                    default:
                        CurrentView = CreateDashboardView();
                        StatusMessage = "لوحة التحكم";
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التنقل: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateCurrentUser()
        {
            // هذا مؤقت - سيتم تحديثه لاحقاً للحصول على المستخدم الحالي من خدمة المصادقة
            CurrentUserName = "مدير النظام";
            IsAdmin = true;
        }

        private object CreateDashboardView()
        {
            return new Views.UserControls.DashboardView();
        }

        private object CreatePatientsView()
        {
            return new Views.UserControls.PatientsView();
        }

        private object CreateTestOrdersView()
        {
            return new Views.UserControls.TestOrdersView();
        }

        private object CreateTestResultsView()
        {
            return new Views.UserControls.TestResultsView();
        }

        private object CreateReportsView()
        {
            return new Views.UserControls.ReportsView();
        }

        private object CreateTestTypesView()
        {
            return new Views.UserControls.TestTypesView();
        }

        private object CreateUsersView()
        {
            return new Views.UserControls.UsersView();
        }

        private object CreateSettingsView()
        {
            return new Views.UserControls.SettingsView();
        }
    }
}
