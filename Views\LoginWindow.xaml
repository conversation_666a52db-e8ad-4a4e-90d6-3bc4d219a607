<Window x:Class="LabManagementSystem.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام إدارة مختبر التحليلات المرضية" 
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Margin="20,20,20,10"
                Background="LightBlue"
                CornerRadius="5">
            <StackPanel Orientation="Vertical" Margin="20">
                <TextBlock Text="🏥"
                          FontSize="48"
                          HorizontalAlignment="Center"/>
                <TextBlock Text="نظام إدارة مختبر التحليلات المرضية"
                          FontSize="18" FontWeight="Bold"
                          HorizontalAlignment="Center"
                          Margin="0,10,0,0"/>
                <TextBlock Text="Laboratory Management System"
                          FontSize="12"
                          HorizontalAlignment="Center"
                          Foreground="DarkBlue"/>
            </StackPanel>
        </Border>

        <!-- Login Form -->
        <Border Grid.Row="1" Margin="20,10,20,10"
                Background="White"
                BorderBrush="LightGray"
                BorderThickness="1"
                CornerRadius="5">
            <StackPanel Margin="30">
                <TextBlock Text="تسجيل الدخول"
                          FontSize="24" FontWeight="Bold"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,30"/>

                <!-- Username -->
                <TextBlock Text="اسم المستخدم:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,20"
                        FontSize="14"
                        Height="30"
                        Padding="5"/>

                <!-- Password -->
                <TextBlock Text="كلمة المرور:" FontWeight="Bold" Margin="0,0,0,5"/>
                <PasswordBox x:Name="PasswordBox"
                            Margin="0,0,0,20"
                            FontSize="14"
                            Height="30"
                            PasswordChanged="PasswordBox_PasswordChanged"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}"
                          Foreground="Red"
                          FontSize="12"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,10"
                          Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

                <!-- Login Button -->
                <Button Content="دخول"
                       Command="{Binding LoginCommand}"
                       IsDefault="True"
                       Background="DodgerBlue"
                       Foreground="White"
                       Height="40"
                       FontSize="14"
                       Margin="0,10,0,0"
                       IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"/>

                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                           Margin="0,10,0,0"/>
            </StackPanel>
        </Border>

        <!-- Footer -->
        <StackPanel Grid.Row="2" Orientation="Horizontal"
                   HorizontalAlignment="Center" Margin="20">
            <Button Content="خروج"
                   Command="{Binding ExitCommand}"
                   Background="Gray"
                   Foreground="White"
                   Padding="10,5"
                   Margin="10,0"/>
        </StackPanel>
    </Grid>
</Window>
