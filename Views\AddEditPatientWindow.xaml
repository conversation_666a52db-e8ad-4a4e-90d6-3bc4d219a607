<Window x:Class="LabManagementSystem.Views.AddEditPatientWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إدارة المريض" 
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                  Text="{Binding WindowTitle}" 
                  FontSize="24" FontWeight="Bold"
                  HorizontalAlignment="Center" 
                  Margin="0,0,0,20"/>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0,0,0,20">
                <!-- Full Name -->
                <TextBox materialDesign:HintAssist.Hint="الاسم الكامل *"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding Patient.FullName, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,15"/>

                <!-- Date of Birth -->
                <DatePicker materialDesign:HintAssist.Hint="تاريخ الميلاد *"
                           materialDesign:HintAssist.IsFloating="True"
                           SelectedDate="{Binding Patient.DateOfBirth}"
                           Margin="0,0,0,15"/>

                <!-- Gender -->
                <ComboBox materialDesign:HintAssist.Hint="الجنس *"
                         materialDesign:HintAssist.IsFloating="True"
                         SelectedValue="{Binding Patient.Gender}"
                         Margin="0,0,0,15">
                    <ComboBoxItem Content="ذكر" Tag="Male"/>
                    <ComboBoxItem Content="أنثى" Tag="Female"/>
                </ComboBox>

                <!-- Phone -->
                <TextBox materialDesign:HintAssist.Hint="رقم الهاتف"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding Patient.Phone, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,15"/>

                <!-- Email -->
                <TextBox materialDesign:HintAssist.Hint="البريد الإلكتروني"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding Patient.Email, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,15"/>

                <!-- National ID -->
                <TextBox materialDesign:HintAssist.Hint="الرقم القومي"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding Patient.NationalId, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,15"/>

                <!-- Address -->
                <TextBox materialDesign:HintAssist.Hint="العنوان"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding Patient.Address, UpdateSourceTrigger=PropertyChanged}"
                        AcceptsReturn="True"
                        TextWrapping="Wrap"
                        MinLines="2"
                        MaxLines="4"
                        Margin="0,0,0,15"/>

                <!-- Emergency Contact -->
                <TextBox materialDesign:HintAssist.Hint="جهة الاتصال في حالات الطوارئ"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding Patient.EmergencyContact, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,15"/>

                <!-- Emergency Phone -->
                <TextBox materialDesign:HintAssist.Hint="هاتف الطوارئ"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding Patient.EmergencyPhone, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,15"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}" 
                          Foreground="Red" 
                          FontSize="12"
                          Margin="0,10,0,0"
                          Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                   HorizontalAlignment="Center">
            <Button Content="حفظ" 
                   Command="{Binding SaveCommand}"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   materialDesign:ButtonAssist.CornerRadius="20"
                   Width="100"
                   Margin="0,0,10,0"
                   IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"/>

            <Button Content="إلغاء" 
                   Command="{Binding CancelCommand}"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   materialDesign:ButtonAssist.CornerRadius="20"
                   Width="100"
                   Margin="10,0,0,0"/>
        </StackPanel>

        <!-- Loading Indicator -->
        <ProgressBar Grid.Row="1" 
                    IsIndeterminate="True" 
                    Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                    VerticalAlignment="Bottom"
                    Margin="0,0,0,10"/>
    </Grid>
</Window>
