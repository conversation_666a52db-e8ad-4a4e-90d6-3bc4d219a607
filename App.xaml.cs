using System.Configuration;
using System.Data;
using System.Windows;
using LabManagementSystem.Data;
using LabManagementSystem.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace LabManagementSystem
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        public IHost? Host { get; private set; }

        protected override void OnStartup(StartupEventArgs e)
        {
            Host = Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // إضافة قاعدة البيانات
                    services.AddDbContext<LabDbContext>(options =>
                        options.UseSqlite("Data Source=lab_database.db"));

                    // إضافة الخدمات
                    services.AddSingleton<AuthenticationService>();
                    services.AddScoped<PatientService>();
                    services.AddTransient<Views.LoginWindow>();
                    services.AddTransient<Views.MainWindow>();
                })
                .Build();

            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            using (var scope = Host.Services.CreateScope())
            {
                var context = scope.ServiceProvider.GetRequiredService<LabDbContext>();
                context.Database.EnsureCreated();
            }

            base.OnStartup(e);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            Host?.Dispose();
            base.OnExit(e);
        }
    }
}
