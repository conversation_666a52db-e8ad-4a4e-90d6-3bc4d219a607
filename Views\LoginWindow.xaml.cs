using System;
using System.Windows;
using System.Windows.Controls;
using LabManagementSystem.Services;
using LabManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace LabManagementSystem.Views
{
    /// <summary>
    /// Interaction logic for LoginWindow.xaml
    /// </summary>
    public partial class LoginWindow : Window
    {
        private AuthenticationService? _authService;

        public LoginWindow()
        {
            InitializeComponent();

            // إنشاء الخدمات المطلوبة
            try
            {
                var options = new DbContextOptionsBuilder<LabDbContext>()
                    .UseSqlite("Data Source=lab_database.db")
                    .Options;
                var dbContext = new LabDbContext(options);
                _authService = new AuthenticationService(dbContext);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النظام: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            // لا حاجة لشيء هنا الآن
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            if (_authService == null)
            {
                MessageBox.Show("خطأ في النظام", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            var username = UsernameBox.Text;
            var password = PasswordBox.Password;

            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
            {
                ErrorText.Text = "يرجى إدخال اسم المستخدم وكلمة المرور";
                return;
            }

            LoginButton.IsEnabled = false;
            LoadingText.Visibility = Visibility.Visible;
            ErrorText.Text = "";

            try
            {
                var success = await _authService.LoginAsync(username, password);

                if (success)
                {
                    // فتح النافذة الرئيسية
                    var mainWindow = new MainWindow();
                    mainWindow.Show();
                    this.Close();
                }
                else
                {
                    ErrorText.Text = "اسم المستخدم أو كلمة المرور غير صحيحة";
                }
            }
            catch (Exception ex)
            {
                ErrorText.Text = "حدث خطأ أثناء تسجيل الدخول";
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                LoginButton.IsEnabled = true;
                LoadingText.Visibility = Visibility.Collapsed;
            }
        }

        private void ExitButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }
    }
}
