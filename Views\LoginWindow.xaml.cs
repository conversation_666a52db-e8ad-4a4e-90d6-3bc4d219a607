using System;
using System.Windows;
using System.Windows.Controls;
using LabManagementSystem.ViewModels;
using LabManagementSystem.Services;
using LabManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace LabManagementSystem.Views
{
    /// <summary>
    /// Interaction logic for LoginWindow.xaml
    /// </summary>
    public partial class LoginWindow : Window
    {
        private LoginViewModel? _viewModel;

        public LoginWindow()
        {
            InitializeComponent();

            // إنشاء الخدمات المطلوبة
            try
            {
                var options = new DbContextOptionsBuilder<LabDbContext>()
                    .UseSqlite("Data Source=lab_database.db")
                    .Options;
                var dbContext = new LabDbContext(options);
                var authService = new AuthenticationService(dbContext);
                _viewModel = new LoginViewModel(authService);
                DataContext = _viewModel;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النظام: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (DataContext is LoginViewModel viewModel)
            {
                viewModel.Password = ((PasswordBox)sender).Password;
            }
        }
    }
}
