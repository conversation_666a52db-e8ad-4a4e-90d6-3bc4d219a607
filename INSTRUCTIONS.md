# تعليمات تشغيل نظام إدارة مختبر التحليلات المرضية

## طريقة التشغيل السريع

### الطريقة الأولى: استخدام ملف التشغيل التلقائي
1. انقر نقراً مزدوجاً على ملف `run.bat`
2. سيتم تشغيل البرنامج تلقائياً

### الطريقة الثانية: استخدام سطر الأوامر
```bash
# فتح سطر الأوامر في مجلد المشروع
cd lab

# تشغيل البرنامج
dotnet run
```

## بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

## الميزات المتاحة حالياً

### ✅ مكتملة ومتاحة للاستخدام
1. **نظام تسجيل الدخول**
   - مصادقة المستخدمين
   - إدارة الجلسات

2. **لوحة التحكم**
   - عرض إحصائيات سريعة
   - أزرار الإجراءات السريعة

3. **إدارة المرضى** (مكتملة)
   - إضافة مريض جديد
   - تعديل بيانات المريض
   - عرض تفاصيل المريض
   - حذف المريض
   - البحث في المرضى
   - التحقق من عدم تكرار الرقم القومي

### 🚧 قيد التطوير (واجهات أساسية موجودة)
- إدارة طلبات الفحص
- إدخال النتائج
- التقارير
- إدارة أنواع التحليلات
- إدارة المستخدمين
- الإعدادات

## استكشاف الأخطاء

### مشكلة: "dotnet غير معروف"
**الحل:** تثبيت .NET 8.0 SDK من الرابط:
https://dotnet.microsoft.com/download/dotnet/8.0

### مشكلة: خطأ في قاعدة البيانات
**الحل:** حذف ملف `lab_database.db` وإعادة تشغيل البرنامج

### مشكلة: خطأ في الحزم
**الحل:** تشغيل الأوامر التالية:
```bash
dotnet clean
dotnet restore
dotnet build
```

## ملفات قاعدة البيانات
- `lab_database.db` - قاعدة البيانات الرئيسية (SQLite)
- يتم إنشاؤها تلقائياً عند أول تشغيل

## النسخ الاحتياطي
لعمل نسخة احتياطية، انسخ ملف `lab_database.db` إلى مكان آمن.

## الدعم الفني
هذا مشروع تعليمي/تجريبي. للمساعدة:
1. تحقق من ملف README.md
2. راجع رسائل الخطأ في سطر الأوامر
3. تأكد من تثبيت .NET 8.0 SDK

## متطلبات النظام
- Windows 10 أو أحدث
- .NET 8.0 SDK
- 100 ميجابايت مساحة فارغة
- 4 جيجابايت رام (الحد الأدنى)
