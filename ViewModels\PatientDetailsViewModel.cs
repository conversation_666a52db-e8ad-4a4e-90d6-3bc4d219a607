using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LabManagementSystem.Models;
using System.Windows;

namespace LabManagementSystem.ViewModels
{
    public partial class PatientDetailsViewModel : ObservableObject
    {
        private readonly Window _window;

        [ObservableProperty]
        private Patient patient;

        public string GenderText => Patient.Gender == Gender.Male ? "ذكر" : "أنثى";

        public PatientDetailsViewModel(Patient patient, Window window)
        {
            Patient = patient;
            _window = window;
        }

        [RelayCommand]
        private void Edit()
        {
            try
            {
                var editWindow = new Views.AddEditPatientWindow(Patient);
                var result = editWindow.ShowDialog();
                
                if (result == true)
                {
                    // إغلاق نافذة التفاصيل بعد التعديل
                    _window.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة التعديل: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void Close()
        {
            _window.Close();
        }
    }
}
