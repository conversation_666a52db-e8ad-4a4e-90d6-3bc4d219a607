using System.ComponentModel.DataAnnotations;

namespace LabManagementSystem.Models
{
    public class TestResult
    {
        public int Id { get; set; }
        
        public int TestOrderId { get; set; }
        public virtual TestOrder TestOrder { get; set; } = null!;
        
        public int TestTypeId { get; set; }
        public virtual TestType TestType { get; set; } = null!;
        
        [MaxLength(500)]
        public string? Result { get; set; }
        
        public decimal? NumericResult { get; set; }
        
        [MaxLength(100)]
        public string? Unit { get; set; }
        
        [MaxLength(100)]
        public string? ReferenceRange { get; set; }
        
        public ResultStatus Status { get; set; } = ResultStatus.Normal;
        
        [MaxLength(1000)]
        public string? Comments { get; set; }
        
        public int? TechnicianId { get; set; }
        public virtual User? Technician { get; set; }
        
        public int? ReviewedByDoctorId { get; set; }
        public virtual User? ReviewedByDoctor { get; set; }
        
        public DateTime? ResultDate { get; set; }
        
        public DateTime? ReviewedDate { get; set; }
        
        public bool IsApproved { get; set; } = false;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedAt { get; set; }
    }

    public enum ResultStatus
    {
        Normal = 1,     // طبيعي
        High = 2,       // مرتفع
        Low = 3,        // منخفض
        Critical = 4,   // حرج
        Abnormal = 5    // غير طبيعي
    }
}
