<UserControl x:Class="LabManagementSystem.Views.UserControls.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Welcome Header -->
            <TextBlock Grid.Row="0" 
                      Text="مرحباً بك في نظام إدارة مختبر التحليلات المرضية" 
                      FontSize="24" FontWeight="Bold"
                      HorizontalAlignment="Center" 
                      Margin="0,0,0,30"/>

            <!-- Statistics Cards -->
            <UniformGrid Grid.Row="1" Columns="4" Margin="0,0,0,30">
                <!-- Total Patients -->
                <materialDesign:Card Margin="10" 
                                    materialDesign:ShadowAssist.ShadowDepth="Depth2">
                    <StackPanel Margin="20" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="AccountGroup" 
                                               Width="48" Height="48" 
                                               HorizontalAlignment="Center"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        <TextBlock Text="إجمالي المرضى" 
                                  FontSize="14" FontWeight="Bold"
                                  HorizontalAlignment="Center" 
                                  Margin="0,10,0,5"/>
                        <TextBlock Text="0" 
                                  FontSize="24" FontWeight="Bold"
                                  HorizontalAlignment="Center"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Pending Tests -->
                <materialDesign:Card Margin="10" 
                                    materialDesign:ShadowAssist.ShadowDepth="Depth2">
                    <StackPanel Margin="20" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="TestTube" 
                                               Width="48" Height="48" 
                                               HorizontalAlignment="Center"
                                               Foreground="Orange"/>
                        <TextBlock Text="فحوصات معلقة" 
                                  FontSize="14" FontWeight="Bold"
                                  HorizontalAlignment="Center" 
                                  Margin="0,10,0,5"/>
                        <TextBlock Text="0" 
                                  FontSize="24" FontWeight="Bold"
                                  HorizontalAlignment="Center"
                                  Foreground="Orange"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Completed Tests Today -->
                <materialDesign:Card Margin="10" 
                                    materialDesign:ShadowAssist.ShadowDepth="Depth2">
                    <StackPanel Margin="20" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="CheckCircle" 
                                               Width="48" Height="48" 
                                               HorizontalAlignment="Center"
                                               Foreground="Green"/>
                        <TextBlock Text="فحوصات اليوم" 
                                  FontSize="14" FontWeight="Bold"
                                  HorizontalAlignment="Center" 
                                  Margin="0,10,0,5"/>
                        <TextBlock Text="0" 
                                  FontSize="24" FontWeight="Bold"
                                  HorizontalAlignment="Center"
                                  Foreground="Green"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Revenue Today -->
                <materialDesign:Card Margin="10" 
                                    materialDesign:ShadowAssist.ShadowDepth="Depth2">
                    <StackPanel Margin="20" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="CurrencyUsd" 
                                               Width="48" Height="48" 
                                               HorizontalAlignment="Center"
                                               Foreground="Purple"/>
                        <TextBlock Text="إيرادات اليوم" 
                                  FontSize="14" FontWeight="Bold"
                                  HorizontalAlignment="Center" 
                                  Margin="0,10,0,5"/>
                        <TextBlock Text="0 ج.م" 
                                  FontSize="24" FontWeight="Bold"
                                  HorizontalAlignment="Center"
                                  Foreground="Purple"/>
                    </StackPanel>
                </materialDesign:Card>
            </UniformGrid>

            <!-- Quick Actions -->
            <materialDesign:Card Grid.Row="2" 
                                materialDesign:ShadowAssist.ShadowDepth="Depth2">
                <StackPanel Margin="20">
                    <TextBlock Text="الإجراءات السريعة" 
                              FontSize="18" FontWeight="Bold"
                              Margin="0,0,0,20"/>

                    <UniformGrid Columns="3">
                        <Button Content="إضافة مريض جديد" 
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Margin="10"
                               Height="60">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <materialDesign:PackIcon Kind="AccountPlus" 
                                                               Width="24" Height="24" 
                                                               HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" 
                                                  HorizontalAlignment="Center" 
                                                  Margin="0,5,0,0"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button Content="طلب فحص جديد" 
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Margin="10"
                               Height="60">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <materialDesign:PackIcon Kind="TestTubeEmpty" 
                                                               Width="24" Height="24" 
                                                               HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" 
                                                  HorizontalAlignment="Center" 
                                                  Margin="0,5,0,0"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button Content="إدخال نتائج" 
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Margin="10"
                               Height="60">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <materialDesign:PackIcon Kind="ClipboardEdit" 
                                                               Width="24" Height="24" 
                                                               HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" 
                                                  HorizontalAlignment="Center" 
                                                  Margin="0,5,0,0"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </UniformGrid>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
    </ScrollViewer>
</UserControl>
