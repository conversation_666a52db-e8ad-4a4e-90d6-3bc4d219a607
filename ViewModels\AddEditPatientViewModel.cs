using System;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LabManagementSystem.Data;
using LabManagementSystem.Models;
using LabManagementSystem.Services;
using Microsoft.EntityFrameworkCore;

namespace LabManagementSystem.ViewModels
{
    public partial class AddEditPatientViewModel : ObservableObject
    {
        private readonly PatientService _patientService;
        private readonly Window _window;
        private readonly bool _isEditMode;

        [ObservableProperty]
        private Patient patient = new();

        [ObservableProperty]
        private string windowTitle = "إضافة مريض جديد";

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        public AddEditPatientViewModel(Patient? existingPatient, Window window)
        {
            _window = window;

            // إنشاء خدمة المرضى
            try
            {
                var options = new DbContextOptionsBuilder<LabDbContext>()
                    .UseSqlite("Data Source=lab_database.db")
                    .Options;
                var dbContext = new LabDbContext(options);
                _patientService = new PatientService(dbContext);
            }
            catch (Exception)
            {
                throw new InvalidOperationException("Unable to create database context");
            }

            if (existingPatient != null)
            {
                _isEditMode = true;
                Patient = new Patient
                {
                    Id = existingPatient.Id,
                    FullName = existingPatient.FullName,
                    DateOfBirth = existingPatient.DateOfBirth,
                    Gender = existingPatient.Gender,
                    Phone = existingPatient.Phone,
                    Email = existingPatient.Email,
                    NationalId = existingPatient.NationalId,
                    Address = existingPatient.Address,
                    EmergencyContact = existingPatient.EmergencyContact,
                    EmergencyPhone = existingPatient.EmergencyPhone,
                    CreatedAt = existingPatient.CreatedAt,
                    UpdatedAt = existingPatient.UpdatedAt
                };
                WindowTitle = "تعديل بيانات المريض";
            }
            else
            {
                _isEditMode = false;
                Patient = new Patient
                {
                    DateOfBirth = DateTime.Today.AddYears(-30)
                };
                WindowTitle = "إضافة مريض جديد";
            }
        }

        [RelayCommand]
        private async Task SaveAsync()
        {
            if (!ValidateInput())
                return;

            IsLoading = true;
            ErrorMessage = string.Empty;

            try
            {
                // التحقق من عدم تكرار الرقم القومي
                if (!string.IsNullOrWhiteSpace(Patient.NationalId))
                {
                    var nationalIdExists = await _patientService.IsNationalIdExistsAsync(
                        Patient.NationalId, _isEditMode ? Patient.Id : null);
                    
                    if (nationalIdExists)
                    {
                        ErrorMessage = "الرقم القومي موجود بالفعل";
                        return;
                    }
                }

                bool success;
                if (_isEditMode)
                {
                    success = await _patientService.UpdatePatientAsync(Patient);
                }
                else
                {
                    var addedPatient = await _patientService.AddPatientAsync(Patient);
                    success = addedPatient != null;
                }

                if (success)
                {
                    _window.DialogResult = true;
                    _window.Close();
                }
                else
                {
                    ErrorMessage = "فشل في حفظ بيانات المريض";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حفظ البيانات: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            _window.DialogResult = false;
            _window.Close();
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(Patient.FullName))
            {
                ErrorMessage = "يرجى إدخال الاسم الكامل";
                return false;
            }

            if (Patient.DateOfBirth == default || Patient.DateOfBirth > DateTime.Today)
            {
                ErrorMessage = "يرجى إدخال تاريخ ميلاد صحيح";
                return false;
            }

            if (Patient.Gender == 0)
            {
                ErrorMessage = "يرجى اختيار الجنس";
                return false;
            }

            // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
            if (!string.IsNullOrWhiteSpace(Patient.Email) && !IsValidEmail(Patient.Email))
            {
                ErrorMessage = "يرجى إدخال بريد إلكتروني صحيح";
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
