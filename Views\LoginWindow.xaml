<Window x:Class="LabManagementSystem.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام إدارة مختبر التحليلات المرضية" 
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="20,20,20,10" 
                            materialDesign:ShadowAssist.ShadowDepth="Depth2">
            <StackPanel Orientation="Vertical" Margin="20">
                <materialDesign:PackIcon Kind="Hospital" 
                                       Width="48" Height="48" 
                                       HorizontalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                <TextBlock Text="نظام إدارة مختبر التحليلات المرضية" 
                          FontSize="18" FontWeight="Bold"
                          HorizontalAlignment="Center" 
                          Margin="0,10,0,0"/>
                <TextBlock Text="Laboratory Management System" 
                          FontSize="12" 
                          HorizontalAlignment="Center" 
                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Login Form -->
        <materialDesign:Card Grid.Row="1" Margin="20,10,20,10" 
                            materialDesign:ShadowAssist.ShadowDepth="Depth2">
            <StackPanel Margin="30">
                <TextBlock Text="تسجيل الدخول" 
                          FontSize="24" FontWeight="Bold"
                          HorizontalAlignment="Center" 
                          Margin="0,0,0,30"/>

                <!-- Username -->
                <TextBox materialDesign:HintAssist.Hint="اسم المستخدم"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,20"
                        FontSize="14"/>

                <!-- Password -->
                <PasswordBox x:Name="PasswordBox"
                            materialDesign:HintAssist.Hint="كلمة المرور"
                            materialDesign:HintAssist.IsFloating="True"
                            Margin="0,0,0,20"
                            FontSize="14"
                            PasswordChanged="PasswordBox_PasswordChanged"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}" 
                          Foreground="Red" 
                          FontSize="12"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,10"
                          Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

                <!-- Login Button -->
                <Button Content="دخول" 
                       Command="{Binding LoginCommand}"
                       IsDefault="True"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       materialDesign:ButtonAssist.CornerRadius="20"
                       Height="40"
                       FontSize="14"
                       Margin="0,10,0,0">
                    <Button.IsEnabled>
                        <MultiBinding Converter="{StaticResource BooleanAndConverter}">
                            <Binding Path="IsLoading" Converter="{StaticResource InverseBooleanConverter}"/>
                            <Binding Path="Username" Converter="{StaticResource StringToBooleanConverter}"/>
                            <Binding Path="Password" Converter="{StaticResource StringToBooleanConverter}"/>
                        </MultiBinding>
                    </Button.IsEnabled>
                </Button>

                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True" 
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                           Margin="0,10,0,0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Footer -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                   HorizontalAlignment="Center" Margin="20">
            <Button Content="خروج" 
                   Command="{Binding ExitCommand}"
                   Style="{StaticResource MaterialDesignFlatButton}"
                   Margin="10,0"/>
        </StackPanel>
    </Grid>
</Window>
