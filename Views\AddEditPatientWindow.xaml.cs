using System.Windows;
using LabManagementSystem.Models;
using LabManagementSystem.ViewModels;

namespace LabManagementSystem.Views
{
    /// <summary>
    /// Interaction logic for AddEditPatientWindow.xaml
    /// </summary>
    public partial class AddEditPatientWindow : Window
    {
        public AddEditPatientWindow(Patient? patient = null)
        {
            InitializeComponent();
            DataContext = new AddEditPatientViewModel(patient, this);
        }
    }
}
