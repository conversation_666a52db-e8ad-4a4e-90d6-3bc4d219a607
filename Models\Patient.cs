using System.ComponentModel.DataAnnotations;

namespace LabManagementSystem.Models
{
    public class Patient
    {
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string FullName { get; set; } = string.Empty;
        
        public DateTime DateOfBirth { get; set; }
        
        public Gender Gender { get; set; }
        
        [MaxLength(20)]
        public string? Phone { get; set; }
        
        [MaxLength(100)]
        public string? Email { get; set; }
        
        [MaxLength(200)]
        public string? Address { get; set; }
        
        [MaxLength(20)]
        public string? NationalId { get; set; }
        
        [MaxLength(100)]
        public string? EmergencyContact { get; set; }
        
        [MaxLength(20)]
        public string? EmergencyPhone { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedAt { get; set; }
        
        // Navigation properties
        public virtual ICollection<TestOrder> TestOrders { get; set; } = new List<TestOrder>();
    }

    public enum Gender
    {
        Male = 1,
        Female = 2
    }
}
