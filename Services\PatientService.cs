using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using LabManagementSystem.Data;
using LabManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace LabManagementSystem.Services
{
    public class PatientService
    {
        private readonly LabDbContext _context;

        public PatientService(LabDbContext context)
        {
            _context = context;
        }

        public async Task<List<Patient>> GetAllPatientsAsync()
        {
            return await _context.Patients
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();
        }

        public async Task<Patient?> GetPatientByIdAsync(int id)
        {
            return await _context.Patients
                .Include(p => p.TestOrders)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<List<Patient>> SearchPatientsAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllPatientsAsync();

            return await _context.Patients
                .Where(p => p.FullName.Contains(searchTerm) ||
                           p.Phone!.Contains(searchTerm) ||
                           p.NationalId!.Contains(searchTerm) ||
                           p.Email!.Contains(searchTerm))
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();
        }

        public async Task<Patient> AddPatientAsync(Patient patient)
        {
            patient.CreatedAt = DateTime.Now;
            _context.Patients.Add(patient);
            await _context.SaveChangesAsync();
            return patient;
        }

        public async Task<bool> UpdatePatientAsync(Patient patient)
        {
            try
            {
                patient.UpdatedAt = DateTime.Now;
                _context.Patients.Update(patient);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> DeletePatientAsync(int id)
        {
            try
            {
                var patient = await _context.Patients.FindAsync(id);
                if (patient != null)
                {
                    _context.Patients.Remove(patient);
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> IsNationalIdExistsAsync(string nationalId, int? excludePatientId = null)
        {
            if (string.IsNullOrWhiteSpace(nationalId))
                return false;

            var query = _context.Patients.Where(p => p.NationalId == nationalId);
            
            if (excludePatientId.HasValue)
                query = query.Where(p => p.Id != excludePatientId.Value);

            return await query.AnyAsync();
        }

        public async Task<int> GetPatientsCountAsync()
        {
            return await _context.Patients.CountAsync();
        }

        public async Task<List<Patient>> GetRecentPatientsAsync(int count = 10)
        {
            return await _context.Patients
                .OrderByDescending(p => p.CreatedAt)
                .Take(count)
                .ToListAsync();
        }
    }
}
