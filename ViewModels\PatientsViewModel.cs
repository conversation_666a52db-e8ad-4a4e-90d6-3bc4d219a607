using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LabManagementSystem.Models;
using LabManagementSystem.Services;

namespace LabManagementSystem.ViewModels
{
    public partial class PatientsViewModel : ObservableObject
    {
        private readonly PatientService _patientService;

        [ObservableProperty]
        private ObservableCollection<Patient> patients = new();

        [ObservableProperty]
        private Patient? selectedPatient;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string statusMessage = "جاهز";

        public PatientsViewModel(PatientService patientService)
        {
            _patientService = patientService;
            _ = LoadPatientsAsync();
        }

        [RelayCommand]
        private async Task LoadPatientsAsync()
        {
            IsLoading = true;
            StatusMessage = "جاري تحميل المرضى...";

            try
            {
                var patientsList = await _patientService.GetAllPatientsAsync();
                Patients.Clear();
                foreach (var patient in patientsList)
                {
                    Patients.Add(patient);
                }
                StatusMessage = $"تم تحميل {Patients.Count} مريض";
            }
            catch (Exception ex)
            {
                StatusMessage = "خطأ في تحميل المرضى";
                MessageBox.Show($"خطأ في تحميل المرضى: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task SearchPatientsAsync()
        {
            IsLoading = true;
            StatusMessage = "جاري البحث...";

            try
            {
                var patientsList = await _patientService.SearchPatientsAsync(SearchText);
                Patients.Clear();
                foreach (var patient in patientsList)
                {
                    Patients.Add(patient);
                }
                StatusMessage = $"تم العثور على {Patients.Count} مريض";
            }
            catch (Exception ex)
            {
                StatusMessage = "خطأ في البحث";
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void AddPatient()
        {
            try
            {
                var addPatientWindow = new Views.AddEditPatientWindow();
                var result = addPatientWindow.ShowDialog();
                
                if (result == true)
                {
                    _ = LoadPatientsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة المريض: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void EditPatient(Patient? patient)
        {
            if (patient == null) return;

            try
            {
                var editPatientWindow = new Views.AddEditPatientWindow(patient);
                var result = editPatientWindow.ShowDialog();
                
                if (result == true)
                {
                    _ = LoadPatientsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تعديل المريض: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task DeletePatientAsync(Patient? patient)
        {
            if (patient == null) return;

            var result = MessageBox.Show(
                $"هل تريد حذف المريض '{patient.FullName}'؟\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                IsLoading = true;
                StatusMessage = "جاري حذف المريض...";

                try
                {
                    var success = await _patientService.DeletePatientAsync(patient.Id);
                    if (success)
                    {
                        Patients.Remove(patient);
                        StatusMessage = "تم حذف المريض بنجاح";
                    }
                    else
                    {
                        StatusMessage = "فشل في حذف المريض";
                        MessageBox.Show("فشل في حذف المريض", "خطأ", 
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    StatusMessage = "خطأ في حذف المريض";
                    MessageBox.Show($"خطأ في حذف المريض: {ex.Message}", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        [RelayCommand]
        private void ViewPatient(Patient? patient)
        {
            if (patient == null) return;

            try
            {
                var viewPatientWindow = new Views.PatientDetailsWindow(patient);
                viewPatientWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تفاصيل المريض: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        partial void OnSearchTextChanged(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                _ = LoadPatientsAsync();
            }
        }
    }
}
