using System.Windows;
using System.Windows.Controls;
using LabManagementSystem.ViewModels;
using LabManagementSystem.Services;
using LabManagementSystem.Data;
using Microsoft.Extensions.DependencyInjection;

namespace LabManagementSystem.Views
{
    /// <summary>
    /// Interaction logic for LoginWindow.xaml
    /// </summary>
    public partial class LoginWindow : Window
    {
        private LoginViewModel _viewModel;

        public LoginWindow()
        {
            InitializeComponent();
            
            // إنشاء الخدمات المطلوبة
            var serviceProvider = ((App)Application.Current).Host?.Services;
            if (serviceProvider != null)
            {
                var dbContext = serviceProvider.GetRequiredService<LabDbContext>();
                var authService = new AuthenticationService(dbContext);
                _viewModel = new LoginViewModel(authService);
                DataContext = _viewModel;
            }
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (DataContext is LoginViewModel viewModel)
            {
                viewModel.Password = ((PasswordBox)sender).Password;
            }
        }
    }
}
