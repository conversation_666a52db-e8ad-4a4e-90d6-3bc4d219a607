using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LabManagementSystem.Services;
using System.Windows;

namespace LabManagementSystem.ViewModels
{
    public partial class LoginViewModel : ObservableObject
    {
        private readonly AuthenticationService _authService;

        [ObservableProperty]
        private string username = string.Empty;

        [ObservableProperty]
        private string password = string.Empty;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        public LoginViewModel(AuthenticationService authService)
        {
            _authService = authService;
        }

        [RelayCommand]
        private async Task LoginAsync()
        {
            if (string.IsNullOrWhiteSpace(Username) || string.IsNullOrWhiteSpace(Password))
            {
                ErrorMessage = "يرجى إدخال اسم المستخدم وكلمة المرور";
                return;
            }

            IsLoading = true;
            ErrorMessage = string.Empty;

            try
            {
                var success = await _authService.LoginAsync(Username, Password);

                if (success)
                {
                    // إغلاق نافذة تسجيل الدخول وفتح النافذة الرئيسية
                    var mainWindow = new Views.MainWindow();
                    mainWindow.Show();

                    // إغلاق نافذة تسجيل الدخول
                    Application.Current.Windows.OfType<Views.LoginWindow>().FirstOrDefault()?.Close();
                }
                else
                {
                    ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "حدث خطأ أثناء تسجيل الدخول: " + ex.Message;
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void Exit()
        {
            Application.Current.Shutdown();
        }

        partial void OnUsernameChanged(string value)
        {
            ErrorMessage = string.Empty;
        }

        partial void OnPasswordChanged(string value)
        {
            ErrorMessage = string.Empty;
        }
    }
}
