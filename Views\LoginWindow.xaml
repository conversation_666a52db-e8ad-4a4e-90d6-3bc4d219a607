<Window x:Class="LabManagementSystem.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول - نظام إدارة مختبر التحليلات المرضية"
        Height="400" Width="350"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="LightGray"
        FlowDirection="RightToLeft">

    <StackPanel Margin="30" VerticalAlignment="Center">
        <!-- Header -->
        <TextBlock Text="🏥 نظام إدارة مختبر التحليلات المرضية"
                  FontSize="16" FontWeight="Bold"
                  HorizontalAlignment="Center"
                  Margin="0,0,0,30"
                  TextWrapping="Wrap"/>

        <TextBlock Text="تسجيل الدخول"
                  FontSize="20" FontWeight="Bold"
                  HorizontalAlignment="Center"
                  Margin="0,0,0,30"/>

        <!-- Username -->
        <TextBlock Text="اسم المستخدم:" FontWeight="Bold" Margin="0,0,0,5"/>
        <TextBox x:Name="UsernameBox"
                Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                Margin="0,0,0,15"
                FontSize="14"
                Height="35"
                Padding="8"
                BorderBrush="Gray"
                BorderThickness="1"/>

        <!-- Password -->
        <TextBlock Text="كلمة المرور:" FontWeight="Bold" Margin="0,0,0,5"/>
        <PasswordBox x:Name="PasswordBox"
                    Margin="0,0,0,15"
                    FontSize="14"
                    Height="35"
                    BorderBrush="Gray"
                    BorderThickness="1"
                    PasswordChanged="PasswordBox_PasswordChanged"/>

        <!-- Error Message -->
        <TextBlock x:Name="ErrorText"
                  Text="{Binding ErrorMessage}"
                  Foreground="Red"
                  FontSize="12"
                  HorizontalAlignment="Center"
                  Margin="0,0,0,15"
                  TextWrapping="Wrap"/>

        <!-- Login Button -->
        <Button x:Name="LoginButton"
               Content="دخول"
               Click="LoginButton_Click"
               Background="Blue"
               Foreground="White"
               Height="40"
               FontSize="16"
               FontWeight="Bold"
               Margin="0,10,0,15"/>

        <!-- Exit Button -->
        <Button Content="خروج"
               Click="ExitButton_Click"
               Background="Gray"
               Foreground="White"
               Height="35"
               FontSize="14"
               Margin="0,5,0,0"/>

        <!-- Loading -->
        <TextBlock x:Name="LoadingText"
                  Text="جاري تسجيل الدخول..."
                  HorizontalAlignment="Center"
                  Margin="0,10,0,0"
                  Visibility="Collapsed"/>
    </StackPanel>
</Window>
