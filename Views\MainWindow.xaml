<Window x:Class="LabManagementSystem.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="نظام إدارة مختبر التحليلات المرضية" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" 
                            materialDesign:ShadowAssist.ShadowDepth="Depth2"
                            Margin="5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="20,10">
                    <materialDesign:PackIcon Kind="Hospital" 
                                           Width="32" Height="32" 
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="نظام إدارة مختبر التحليلات المرضية" 
                              FontSize="20" FontWeight="Bold"
                              VerticalAlignment="Center" 
                              Margin="10,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20,10">
                    <TextBlock Text="{Binding CurrentUserName}" 
                              VerticalAlignment="Center" 
                              Margin="0,0,10,0"/>
                    <Button Content="تسجيل الخروج" 
                           Command="{Binding LogoutCommand}"
                           Style="{StaticResource MaterialDesignFlatButton}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Navigation Menu -->
            <materialDesign:Card Grid.Column="0" 
                                materialDesign:ShadowAssist.ShadowDepth="Depth2"
                                Margin="0,0,5,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <TextBlock Text="القائمة الرئيسية" 
                                  FontSize="16" FontWeight="Bold"
                                  Margin="10,10,10,20"/>

                        <!-- Dashboard -->
                        <Button Content="لوحة التحكم" 
                               Command="{Binding NavigateCommand}"
                               CommandParameter="Dashboard"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Right"
                               Margin="0,2">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ViewDashboard" 
                                                               Width="20" Height="20" 
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" 
                                                  Margin="10,0,0,0" 
                                                  VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <!-- Patients -->
                        <Button Content="إدارة المرضى" 
                               Command="{Binding NavigateCommand}"
                               CommandParameter="Patients"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Right"
                               Margin="0,2">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="AccountGroup" 
                                                               Width="20" Height="20" 
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" 
                                                  Margin="10,0,0,0" 
                                                  VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <!-- Test Orders -->
                        <Button Content="طلبات الفحص" 
                               Command="{Binding NavigateCommand}"
                               CommandParameter="TestOrders"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Right"
                               Margin="0,2">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="TestTube" 
                                                               Width="20" Height="20" 
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" 
                                                  Margin="10,0,0,0" 
                                                  VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <!-- Test Results -->
                        <Button Content="النتائج" 
                               Command="{Binding NavigateCommand}"
                               CommandParameter="TestResults"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Right"
                               Margin="0,2">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ClipboardText" 
                                                               Width="20" Height="20" 
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" 
                                                  Margin="10,0,0,0" 
                                                  VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <!-- Reports -->
                        <Button Content="التقارير" 
                               Command="{Binding NavigateCommand}"
                               CommandParameter="Reports"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Right"
                               Margin="0,2">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FileChart" 
                                                               Width="20" Height="20" 
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" 
                                                  Margin="10,0,0,0" 
                                                  VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <!-- Test Types -->
                        <Button Content="أنواع التحليلات" 
                               Command="{Binding NavigateCommand}"
                               CommandParameter="TestTypes"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Right"
                               Margin="0,2">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Flask" 
                                                               Width="20" Height="20" 
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" 
                                                  Margin="10,0,0,0" 
                                                  VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <!-- Users (Admin only) -->
                        <Button Content="إدارة المستخدمين" 
                               Command="{Binding NavigateCommand}"
                               CommandParameter="Users"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Right"
                               Margin="0,2"
                               Visibility="{Binding IsAdmin, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="AccountSettings" 
                                                               Width="20" Height="20" 
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" 
                                                  Margin="10,0,0,0" 
                                                  VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <!-- Settings -->
                        <Button Content="الإعدادات" 
                               Command="{Binding NavigateCommand}"
                               CommandParameter="Settings"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Right"
                               Margin="0,2">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Settings" 
                                                               Width="20" Height="20" 
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" 
                                                  Margin="10,0,0,0" 
                                                  VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>

            <!-- Content Area -->
            <materialDesign:Card Grid.Column="1" 
                                materialDesign:ShadowAssist.ShadowDepth="Depth2"
                                Margin="5,0,0,0">
                <ContentControl Content="{Binding CurrentView}" />
            </materialDesign:Card>
        </Grid>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Background="{DynamicResource MaterialDesignDarkBackground}">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" 
                          Foreground="White"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Left">
                <TextBlock Text="{Binding CurrentDateTime, StringFormat='yyyy/MM/dd HH:mm:ss'}" 
                          Foreground="White"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
